version: 2.1

executors:
  node18-small:
    docker:
      - image: cimg/node:18.20
    resource_class: small

jobs:
  install_dependencies:
    executor: node18-small
    steps:
      - checkout
      - restore_cache:
          keys:
            - v2-dependencies-{{ checksum "package-lock.json" }}-{{ checksum "projects/web/package-lock.json" }}
            - v2-dependencies-{{ checksum "package-lock.json" }}-
            - v2-dependencies-
      - run:
          name: Install dependencies
          command: npm ci
      - save_cache:
          paths:
            - node_modules
            - projects/web/node_modules
            - ~/.npm
          key: v2-dependencies-{{ checksum "package-lock.json" }}-{{ checksum "projects/web/package-lock.json" }}
      - persist_to_workspace:
          root: .
          paths:
            - node_modules
            - projects/web/node_modules

  build:
    executor: node18-small
    steps:
      - checkout
      - attach_workspace:
          at: .
      - restore_cache:
          keys:
            - v1-nextjs-build-{{ .Branch }}-{{ .Revision }}
            - v1-nextjs-build-{{ .Branch }}-
            - v1-nextjs-build-
      - run:
          name: Build application
          command: npm run build
      - save_cache:
          paths:
            - projects/web/.next/cache
          key: v1-nextjs-build-{{ .Branch }}-{{ .Revision }}

  lint:
    executor: node18-small
    steps:
      - checkout
      - attach_workspace:
          at: .
      - run:
          name: Run linter
          command: npm run lint

  deploy_staging:
    executor: node18-small
    steps:
      - checkout
      - attach_workspace:
          at: .
      - run:
          name: Install TiDB Cloud CLI and dependencies
          command: |
            # Install jq for JSON parsing
            sudo apt-get update && sudo apt-get install -y jq
            # Install TiDB Cloud CLI
            curl https://raw.githubusercontent.com/tidbcloud/tidbcloud-cli/main/install.sh | sh
            # Add to PATH
            echo 'export PATH="$HOME/.ticloud/bin:$PATH"' >> $BASH_ENV
      - run:
          name: Configure TiDB Cloud CLI profile
          command: |
            ticloud config create --profile-name ci \
              --public-key  "$TIDB_PUBLIC_KEY" \
              --private-key "$TIDB_PRIVATE_KEY"
      - run:
          name: Set DATABASE_URL for staging
          command: |
            DATABASE_URL=$(./scripts/get-tidb-url.sh staging)
            echo "export DATABASE_URL='$DATABASE_URL'" >> $BASH_ENV
      - run:
          name: Run database migrations
          command: ./scripts/run-migrations.sh staging
      - run:
          name: Deploy to staging
          command: npm run staging:deploy

  deploy_production:
    executor: node18-small
    steps:
      - checkout
      - attach_workspace:
          at: .
      - run:
          name: Install TiDB Cloud CLI and dependencies
          command: |
            # Install jq for JSON parsing
            sudo apt-get update && sudo apt-get install -y jq
            # Install TiDB Cloud CLI
            curl https://raw.githubusercontent.com/tidbcloud/tidbcloud-cli/main/install.sh | sh
            # Add to PATH
            echo 'export PATH="$HOME/.ticloud/bin:$PATH"' >> $BASH_ENV
      - run:
          name: Configure TiDB Cloud CLI profile
          command: |
            ticloud config create --profile-name ci \
              --public-key  "$TIDB_PUBLIC_KEY" \
              --private-key "$TIDB_PRIVATE_KEY"
      - run:
          name: Set DATABASE_URL for production
          command: |
            DATABASE_URL=$(./scripts/get-tidb-url.sh production)
            echo "export DATABASE_URL='$DATABASE_URL'" >> $BASH_ENV
      - run:
          name: Run database migrations
          command: ./scripts/run-migrations.sh production
      - run:
          name: Deploy to production
          command: npm run production:deploy

workflows:
  version: 2
  
  # Workflow for non-main branches (staging deployment only)
  staging_deployment:
    jobs:
      - install_dependencies:
          filters:
            branches:
              ignore: main
      #      - build:
      #          requires:
      #            - install_dependencies
      #          filters:
      #            branches:
      #              ignore: main
      - lint:
          requires:
            - install_dependencies
      - build:
          requires:
            - install_dependencies
      - deploy_staging:
          requires:
            - install_dependencies
            - build
  #            - lint

  # Workflow for main branch (staging then production deployment)
  production_deployment:
    jobs:
      - install_dependencies:
          filters:
            branches:
              only: main
      - build:
          requires:
            - install_dependencies
          filters:
            branches:
              only: main
      - lint:
          requires:
            - install_dependencies
      - deploy_staging:
          requires:
            - build
            - lint
      - deploy_production:
          requires:
            - deploy_staging
          filters:
            branches:
              only: main