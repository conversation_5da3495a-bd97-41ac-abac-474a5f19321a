## Agent Guidelines for Friend Cards

### Build/Test Commands

- `npm run dev` - Start development server with Turbopack
- `npm run build` - Build for production
- `npm run lint` - Run ESLint
- `npm start` - Start production server
- `sst dev` - Start SST development environment
- No test framework configured – check with user before adding tests

### Code Style Guidelines

- **TypeScript**: Strict typing required, use `type` for type aliases
- **Imports**: Use relative imports for local files (`../lib/`), absolute for external packages
- **Components**: PascalCase naming; export as named functions
- **Props**: Use interface/type definitions with descriptive names
- **Styling**: Tailwind CSS classes; prefer utility-first approach. Do NOT use raw CSS.
- **Functions**: camelCase naming; use async/await for promises
- **Files**: kebab-case for files; PascalCase for React components
- **Error Handling**: Use try/catch blocks; throw or return proper error types
- **tRPC**: Use Zod for input validation; ensure type-safe procedures
- **Auth**: OpenAuth integration; use server actions for auth operations

### Framework Stack

- Next.js 15 with App Router
- React 19
- TypeScript
- Tailwind CSS
- tRPC
- SST

### Core mandates

- **Simplicity**: Always implement features in the simplest possible way. Favor clarity and minimalism over complexity.
- **Reuse**: Aim to reuse existing components and utilities in the system. Do not introduce extra dependencies
  unnecessarily—always check if functionality already exists or can be shared.
- **Goal Alignment**: Continuously refer back to the original requirements or user story. Do not implement features
  outside the specified scope without explicit approval.
- **Scope Adherence**: Resist adding functionality or broadening the scope beyond what was requested. Any proposed
  extensions must be discussed and approved.
- **Clarification**: If requirements are ambiguous or unclear, promptly ask the user for clarification rather than
  making assumptions.
- **Context Awareness**: Consider the overall system architecture and dependencies; ensure new code integrates
  seamlessly with existing modules.
- **Official Documentation**: Always consult the latest official documentation for any framework, library, or API before
  implementation to ensure accuracy and best practices.
