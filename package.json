{"private": true, "workspaces": ["projects/web"], "scripts": {"build": "cd projects/web && npm run build", "sst-dev": "sst dev", "lint": "cd projects/web && npm run lint", "start": "cd projects/web && npm run start", "dev": "cd projects/web && npm run dev", "staging:deploy": "sst deploy --stage staging", "staging:destroy": "sst remove --stage staging", "production:deploy": "sst deploy --stage production", "all:deploy": "npm run staging:deploy && npm run production:deploy"}, "dependencies": {"sst": "3.14.11"}, "devDependencies": {"@types/node": "^20", "eslint": "^9", "eslint-config-next": "15.3.1", "typescript": "^5"}}