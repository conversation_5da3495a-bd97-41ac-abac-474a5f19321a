{"version": "5", "dialect": "mysql", "id": "d34d417b-10e3-4337-9807-566f24150857", "prevId": "00000000-0000-0000-0000-000000000000", "tables": {"users": {"name": "users", "columns": {"id": {"name": "id", "type": "char(36)", "primaryKey": false, "notNull": true, "autoincrement": false}, "email": {"name": "email", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true, "autoincrement": false}, "username": {"name": "username", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false, "autoincrement": false}, "createdAt": {"name": "createdAt", "type": "timestamp", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "(now())"}, "updatedAt": {"name": "updatedAt", "type": "timestamp", "primaryKey": false, "notNull": true, "autoincrement": false, "onUpdate": true, "default": "(now())"}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {"users_id": {"name": "users_id", "columns": ["id"]}}, "uniqueConstraints": {"users_username_unique": {"name": "users_username_unique", "columns": ["username"]}}, "checkConstraint": {}}}, "views": {}, "_meta": {"schemas": {}, "tables": {}, "columns": {}}, "internal": {"tables": {}, "indexes": {}}}