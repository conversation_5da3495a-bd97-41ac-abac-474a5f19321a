"use client";

import {QueryClient, QueryClientProvider} from "@tanstack/react-query";
import {httpBatchLink} from "@trpc/client";
import React, {useState} from "react";

import {getUrl, makeQueryClient, trpc} from "./trpc-client";

let browserQueryClient: QueryClient;

function getQueryClient() {
    if (typeof window === 'undefined') {
        // Server: always make a new query client
        return makeQueryClient();
    }
    // Browser: make a new query client if we don't already have one
    // This is very important, so we don't re-make a new client if React
    // suspends during the initial render. This may not be needed if we
    // have a suspense boundary BELOW the creation of the query client
    if (!browserQueryClient) browserQueryClient = makeQueryClient();
    return browserQueryClient;
}

export function TRPCProvider({children}: { children: React.ReactNode }) {
    const queryClient = getQueryClient();
    const [trpcClient] = useState(() => {
        const url = getUrl();
        return trpc.createClient({
            links: url
                ? [
                    httpBatchLink({
                        url,
                    }),
                ]
                : [],
        });
    });

    return (
        <trpc.Provider client={trpcClient} queryClient={queryClient}>
            <QueryClientProvider client={queryClient}>{children}</QueryClientProvider>
        </trpc.Provider>
    );
} 