import {createTRPCReact} from "@trpc/react-query";
import type {AppRouter} from "../app/lib/trpc-router";
import {defaultShouldDehydrateQuery, QueryClient} from "@tanstack/react-query";

// Client tRPC
export const trpc = createTRPCReact<AppRouter>();

export function getUrl() {
    if (typeof window !== 'undefined') {
        return '/api';
    }
}


export function makeQueryClient() {
    return new QueryClient({
        defaultOptions: {
            queries: {
                staleTime: 30 * 1000,
            },
            dehydrate: {
                shouldDehydrateQuery: (query) =>
                    defaultShouldDehydrateQuery(query) ||
                    query.state.status === 'pending',
            },
            hydrate: {},
        },
    })
}