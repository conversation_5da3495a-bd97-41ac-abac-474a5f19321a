import {handle} from "hono/aws-lambda"
import {issuer} from "@openauthjs/openauth"
import {subjects} from "./subjects";
import {MemoryStorage} from "@openauthjs/openauth/storage/memory"
import {GoogleOidcProvider} from "@openauthjs/openauth/provider/google";
import {GOOGLE_CLIENT_ID} from "@/projects/web/server/env";
import {DiscordProvider} from "@openauthjs/openauth/provider/discord";

const getTopLevelDomainPart = (url: string) => {
    const urlParts = url.split(".")
    return `${urlParts[urlParts.length - 2]}.${urlParts[urlParts.length - 1]}`
}

const app = issuer({
    subjects,
    storage: MemoryStorage(),
    allow: async (input) => {
        const redirectURI = input.redirectURI;
        const topLevelDomainPart = getTopLevelDomainPart(redirectURI)
        if (topLevelDomainPart.startsWith("friend-cards.com")) {
            return true
        }

        if (redirectURI.startsWith("http://localhost")) {
            return true
        }

        if (redirectURI.startsWith("https://friend-cards-")) {
            if (topLevelDomainPart.startsWith("vercel.app")) {
                return true
            }
        }

        return redirectURI.startsWith("https://staging.friend-cards.com");
    },
    providers: {
        google: GoogleOidcProvider(
            {
                clientID: GOOGLE_CLIENT_ID,
                scopes: ["openid", "email", "profile"],
            }),
        discord: DiscordProvider({
            clientID: process.env.DISCORD_CLIENT_ID!,
            clientSecret: process.env.DISCORD_CLIENT_SECRET!,
            scopes: ["email", "identify"]
        }),
    },
    success: async (ctx, value) => {
        if (value.provider === "google") {
            const email = "email" in value.id && typeof value.id.email === "string" ? value.id.email : undefined

            if (!email) {
                throw new Error("No email found in ID token")
            }

            return ctx.subject("user", {
                email,
            })
        } else if (value.provider === "discord") {
            const res = await fetch("https://discord.com/api/users/@me", {
                headers: {Authorization: `Bearer ${value.tokenset.access}`},
            });
            const user = await res.json();
            if (!user.email) {
                throw new Error("No email found in ID token")
            }
            return ctx.subject("user", {
                email: user.email,
            })
        } else {
            throw new Error("Invalid provider")
        }
    },
})

export const handler = handle(app)