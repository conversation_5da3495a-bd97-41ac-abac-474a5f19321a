import {eq} from 'drizzle-orm';
import {NewUser, users} from "@/projects/web/database/schema";
import {getDb} from "@/projects/web/database/client";

const db = await getDb()

export async function createUser(newUser: NewUser) {
    const userIds = await db.insert(users).values(newUser).$returningId();

    if (userIds.length === 0) {
        throw new Error('User creation failed');
    } else if (userIds.length > 1) {
        throw new Error('Multiple users created?');
    }

    return userIds[0].id;
}

export async function getUserByEmail(email: string) {
    const user = await db.select().from(users).where(eq(users.email, email));
    return user[0];
}

export async function checkUsernameAvailability(username: string) {
    const existingUser = await db.select().from(users).where(eq(users.username, username));
    return existingUser.length === 0;
}

export async function getUserFrontEndData(email: string) {
    const user = await db.select({
        id: users.id,
        username: users.username,
        dob: users.dob,
    }).from(users)
        .where(eq(users.email, email));
    return user[0];
}

// async function updateUsername(email: string, username: string) {
//     await db
//         .update(users)
//         .set({username})
//         .where(eq(users.email, email));
//     console.log('Username updated successfully');
// }
//
// async function getAllUsers() {
//     const allUsers = await db.select().from(users);
//     return allUsers;
// }

// Example usage:
// await createUser('<EMAIL>');
// await updateUsername('<EMAIL>', 'myusername');
// const user = await getUserByEmail('<EMAIL>');
// const allUsers = await getAllUsers();
