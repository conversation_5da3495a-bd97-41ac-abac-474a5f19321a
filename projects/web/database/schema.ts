import {char, date, mysqlTable, timestamp, varchar} from 'drizzle-orm/mysql-core';

export const users = mysqlTable('users', {
    id: char('id', {length: 36,})
        .primaryKey()
        .notNull()
        .$defaultFn(() => crypto.randomUUID()),
    email: varchar({length: 255}).notNull().unique(),
    dob: date({mode: 'date'}).notNull(),
    username: varchar({length: 255}).unique().notNull(),
    createdAt: timestamp().defaultNow().notNull(),
    updatedAt: timestamp().defaultNow().onUpdateNow().notNull(),
});

export type User = typeof users.$inferSelect;
export type NewUser = typeof users.$inferInsert;
