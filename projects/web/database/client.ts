"use server"

import {connect, Connection} from '@tidbcloud/serverless';
import {drizzle, TiDBServerlessDatabase} from 'drizzle-orm/tidb-serverless';
import {DATABASE_URL} from "@/projects/web/server/env";

let db: TiDBServerlessDatabase<Record<string, never>> & {
    $client: Connection
}

export async function getDb() {
    if (db) {
        return db
    }

    const client = connect({url: DATABASE_URL});
    db = drizzle({client: client});
    return db
}