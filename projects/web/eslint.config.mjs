import {dirname} from "path";
import {fileURLToPath} from "url";
import {FlatCompat} from "@eslint/eslintrc";
import unusedImports from "eslint-plugin-unused-imports";

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

const compat = new FlatCompat({
    baseDirectory: __dirname,
});

const eslintConfig = [
    ...compat.extends("next/core-web-vitals", "next/typescript"),
    {
        plugins: {
            "unused-imports": unusedImports,
        },
        rules: {
            // turn off the originals to avoid duplicate reports
            'no-unused-vars': 'off',
            '@typescript-eslint/no-unused-vars': 'off',

            // flag + autofix unused imports
            'unused-imports/no-unused-imports': 'error',

            // still warn about other unused vars,
            // but let the plugin handle the fix
            'unused-imports/no-unused-vars': [
                'warn',
                {
                    vars: 'all',
                    varsIgnorePattern: '^_',
                    args: 'after-used',
                    argsIgnorePattern: '^_',
                },
            ],
        }
    }
];

export default eslintConfig;
