"use server"

import {createClient} from "@openauthjs/openauth/client"
import {cookies as getCookies} from "next/headers"
import {OPENAUTH_ISSUER} from "./env";

export async function getAuthClient() {
    return createClient({
        clientID: "friend-cards",
        issuer: OPENAUTH_ISSUER,
    })
}


export async function setTokens(accessToken: string, refreshToken: string) {
    const cookies = await getCookies()

    cookies.set({
        name: "access_token",
        value: accessToken,
        httpOnly: true,
        sameSite: "lax",
        path: "/",
        maxAge: 34560000,
    })
    cookies.set({
        name: "refresh_token",
        value: refreshToken,
        httpOnly: true,
        sameSite: "lax",
        path: "/",
        maxAge: 34560000,
    })
}