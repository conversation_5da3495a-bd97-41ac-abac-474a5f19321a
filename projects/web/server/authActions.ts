"use server"

import { redirect } from "next/navigation"
import { cookies as getCookies, headers as getHeaders } from "next/headers"
import { subjects } from "@/projects/web/authServer/subjects"
import { getAuthClient, setTokens } from "./authClient"
import { checkUsernameAvailability, createUser, getUserFrontEndData } from "@/projects/web/database/userQueries"
import { z } from "zod"
import { loginState } from "@/projects/web/shared/constants";
import { cache } from "react";

export const getAuthedUserCached = cache(getAuthedUser)

async function getAuthedUser() {
    // Skip auth during build time
    if (process.env.IS_BUILD === 'true') {
        return {
            type: loginState.anonymous,
        }
    }

    const client = await getAuthClient()
    const cookies = await getCookies()
    const accessToken = cookies.get("access_token")
    const refreshToken = cookies.get("refresh_token")

    if (!accessToken) {
        return {
            type: loginState.anonymous,
        }
    }

    const verified = await client.verify(subjects, accessToken.value, {
        refresh: refreshToken?.value,
    })

    if (verified.err) {
        return {
            type: loginState.verificationError,
        }
    }

    if (verified.tokens) {
        await setTokens(verified.tokens.access, verified.tokens.refresh)
    }

    if (verified.subject.type === "user") {
        const userFrontEndData = await getUserFrontEndData(verified.subject.properties.email)
        if (userFrontEndData) {
            return {
                type: loginState.registeredUser,
                email: verified.subject.properties.email,
                ...userFrontEndData,
            }
        }
    }

    return {
        email: verified.subject.properties.email,
        type: loginState.identifiedUser,
    }
}

export async function login(provider: "google" | "discord") {
    const client = await getAuthClient()
    const cookies = await getCookies()
    const accessToken = cookies.get("access_token")
    const refreshToken = cookies.get("refresh_token")

    if (accessToken) {
        const verified = await client.verify(subjects, accessToken.value, {
            refresh: refreshToken?.value,
        })
        if (!verified.err && verified.tokens) {
            await setTokens(verified.tokens.access, verified.tokens.refresh)
            redirect("/")
        }
    }

    const headers = await getHeaders()
    const host = headers.get("host")
    const protocol = host?.includes("localhost") ? "http" : "https"
    const redirectURI = `${protocol}://${host}/api/auth/callback`;

    const { url } = await client.authorize(
        redirectURI,
        "code",
        {
            provider
        }
    )
    redirect(url)
}

export async function logout() {
    const cookies = await getCookies()
    cookies.delete("access_token")
    cookies.delete("refresh_token")

    redirect("/")
}

const signupSchema = z.object({
    username: z.string().min(3, "Username must be at least 3 characters").max(20, "Username must be at most 20 characters"),
    dob: z.date().refine((date) => {
        const today = new Date()
        const age = today.getFullYear() - date.getFullYear()
        const monthDiff = today.getMonth() - date.getMonth()
        const dayDiff = today.getDate() - date.getDate()

        // Calculate exact age
        let exactAge = age
        if (monthDiff < 0 || (monthDiff === 0 && dayDiff < 0)) {
            exactAge--
        }

        return exactAge >= 18
    }, "You must be at least 18 years old")
})

export async function refreshUserCredentials() {
    const client = await getAuthClient()
    const cookies = await getCookies()
    const refreshToken = cookies.get("refresh_token")
    if (refreshToken) {
        await client.refresh(refreshToken.value)
    }

    redirect("/")
}

export async function checkUsernameAvailable(username: string): Promise<boolean> {
    try {
        return await checkUsernameAvailability(username)
    } catch (error) {
        console.error("Error checking username availability:", error)
        return false
    }
}

export async function createUserAccount(username: string, dob: Date) {
    try {
        const validationResult = signupSchema.safeParse({ username, dob })
        if (!validationResult.success) {
            return {
                success: false,
                error: validationResult.error.errors[0]?.message || "Validation failed"
            }
        }

        const isAvailable = await checkUsernameAvailability(username)
        if (!isAvailable) {
            return { success: false, error: "Username is already taken" }
        }

        const authedUser = await getAuthedUserCached()
        if (authedUser.type !== loginState.identifiedUser) {
            return { success: false, error: "Invalid authentication state" }
        }

        const userId = await createUser({
            email: authedUser.email,
            username,
            dob
        })

        return { success: true, userId }
    } catch (error) {
        console.error("Error creating user account:", error)
        return { success: false, error: "Failed to create account" }
    }
}