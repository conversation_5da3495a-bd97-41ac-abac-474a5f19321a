function getDatabaseUrl() {
    let databaseUrl = process.env.DATABASE_URL;

    // During build time, return a placeholder that won't cause URL parsing errors
    if (!databaseUrl) {
        if (process.env.IS_BUILD === 'true') {
            return "mysql://placeholder:placeholder@placeholder:3306/placeholder";
        }
        console.error("No database URL has been set");
        return "no-database-url-has-been-set"
    }

    if (databaseUrl.includes("sslaccept=strict")) {
        databaseUrl = databaseUrl.replace("sslaccept=strict", `ssl={"minVersion":"TLSv1.2"}`);
    }

    return databaseUrl;
}

export const GOOGLE_CLIENT_ID = process.env.GOOGLE_CLIENT_ID!;
export const OPENAUTH_ISSUER = process.env.IS_BUILD === 'true'
    ? 'https://placeholder.auth.com'
    : process.env.OPENAUTH_ISSUER!;
export const ENV = process.env.SST_STAGE;
export const STAGING_APP_URL_FOR_AUTH = process.env.STAGING_APP_URL_FOR_AUTH;
export const DATABASE_URL = getDatabaseUrl();