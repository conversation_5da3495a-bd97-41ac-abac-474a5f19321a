import {type NextRequest, NextResponse} from "next/server"
import {getAuthClient, setTokens} from "@/projects/web/server/authClient";

export async function GET(req: NextRequest) {
    const client = await getAuthClient()
    try {
        const url = new URL(req.url)
        const code = url.searchParams.get("code")
        if (!code) {
            return NextResponse.json({error: 'Missing authorization code'}, {status: 400})
        }
        const exchanged = await client.exchange(code, `${url.origin}/api/auth/callback`)

        if (exchanged.err) {
            return NextResponse.json(exchanged.err, {status: 400})
        }

        await setTokens(exchanged.tokens.access, exchanged.tokens.refresh)

        return NextResponse.redirect(`${url.origin}/`)
    } catch (error) {
        console.error(error)
        return NextResponse.json(error, {status: 400})
    }
}