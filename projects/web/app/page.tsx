import {getAuthedUserCached} from "../server/authActions";
import {AuthPage} from "./components/AuthPage";
import {UserDashboard} from "./components/UserDashboard";
import {UserSignup} from "@/projects/web/app/components/UserSignup";
import {loginState} from "@/projects/web/shared/constants";
import {DATABASE_URL, OPENAUTH_ISSUER} from "@/projects/web/server/env";

function isEnvStateValid() {
    return DATABASE_URL !== undefined || OPENAUTH_ISSUER !== undefined
}

export default async function Home() {

    if (isEnvStateValid()) {
        return <div>In build.</div>
    }

    const authedUser = await getAuthedUserCached();

    if (authedUser.type === loginState.anonymous || authedUser.type === loginState.verificationError) {
        return <AuthPage/>;
    }

    if (authedUser.type === loginState.registeredUser) {
        return <UserDashboard userEmail={authedUser.email}/>;
    }

    if (authedUser.type === loginState.identifiedUser) {
        return <UserSignup userEmail={authedUser.email}/>;
    }

    return <AuthPage/>;
}
