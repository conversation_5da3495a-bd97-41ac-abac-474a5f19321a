import { getAuthedUserCached } from "../server/authActions";
import { AuthPage } from "./components/AuthPage";
import { UserDashboard } from "./components/UserDashboard";
import { UserSignup } from "@/projects/web/app/components/UserSignup";
import { loginState } from "@/projects/web/shared/constants";

function isBuildTime() {
    return process.env.IS_BUILD === 'true';
}

export default async function Home() {

    if (isBuildTime()) {
        return <div>In build.</div>
    }

    const authedUser = await getAuthedUserCached();

    if (authedUser.type === loginState.anonymous || authedUser.type === loginState.verificationError) {
        return <AuthPage />;
    }

    if (authedUser.type === loginState.registeredUser) {
        return <UserDashboard userEmail={authedUser.email} />;
    }

    if (authedUser.type === loginState.identifiedUser) {
        return <UserSignup userEmail={authedUser.email} />;
    }

    return <AuthPage />;
}
