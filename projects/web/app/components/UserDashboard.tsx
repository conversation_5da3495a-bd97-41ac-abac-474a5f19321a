import {LogoutButton} from "@/projects/web/app/components/LogoutButton";

interface UserDashboardProps {
    userEmail: string;
}

export function UserDashboard({userEmail}: UserDashboardProps) {

    return (
        <div className="min-h-screen bg-white flex items-center justify-center p-4 animate-in fade-in duration-300">
            <div className="w-full max-w-md text-center">
                <div className="mb-8">
                    <h1 className="text-3xl font-bold text-black mb-2">Welcome back!</h1>
                    <p className="text-gray-600 mb-6">You are signed in as:</p>
                    <div className="bg-gray-50 border border-gray-200 rounded-lg p-4 mb-6">
                        <p className="text-black font-medium">{userEmail}</p>
                    </div>
                </div>

                <LogoutButton/>
            </div>
        </div>
    );
} 