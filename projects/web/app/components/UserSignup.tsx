"use client"

import {useState, useTransition} from "react"
import {useForm} from "react-hook-form"
import {zodResolver} from "@hookform/resolvers/zod"
import {z} from "zod"
import {format} from "date-fns"
import {CalendarIcon, Loader2} from "lucide-react"
import {cn} from "@/lib/utils"
import {Calendar} from "../../components/ui/calendar"
import {Card, CardContent, CardDescription, CardHeader, CardTitle} from "../../components/ui/card"
import {Form, FormControl, FormDescription, FormField, FormItem, FormLabel, FormMessage} from "../../components/ui/form"
import {Input} from "../../components/ui/input"
import {Popover, PopoverContent, PopoverTrigger} from "../../components/ui/popover"
import {checkUsernameAvailable, createUserAccount} from "@/projects/web/server/authActions"
import {Button} from "../../components/ui/button"
import {usernameValidation} from "@/projects/web/shared/validations";

const formSchema = z.object({
    username: usernameValidation,
    dob: z.date({
        required_error: "Please select your date of birth",
    }).refine((date) => {
        const today = new Date()
        const age = today.getFullYear() - date.getFullYear()
        const monthDiff = today.getMonth() - date.getMonth()
        const dayDiff = today.getDate() - date.getDate()

        let exactAge = age
        if (monthDiff < 0 || (monthDiff === 0 && dayDiff < 0)) {
            exactAge--
        }

        return exactAge >= 18
    }, "You must be at least 18 years old")
})

type FormData = z.infer<typeof formSchema>

interface UserSignupProps {
    userEmail?: string
}

export function UserSignup({userEmail}: UserSignupProps) {
    const [isUsernameChecking, setIsUsernameChecking] = useState(false)
    const [usernameError, setUsernameError] = useState<string | null>(null)
    const [isUsernameValid, setIsUsernameValid] = useState(false)
    const [isPending, startTransition] = useTransition()
    const [submitError, setSubmitError] = useState<string | null>(null)

    const form = useForm<FormData>({
        resolver: zodResolver(formSchema),
        defaultValues: {
            username: "",
        },
    })

    const checkUsername = async (username: string) => {
        // Always trigger form validation to show length errors
        await form.trigger("username")

        if (username.length < 3) {
            setUsernameError(null)
            setIsUsernameValid(false)
            return
        }

        setIsUsernameChecking(true)
        setUsernameError(null)

        try {
            const isAvailable = await checkUsernameAvailable(username)
            if (isAvailable) {
                setIsUsernameValid(true)
                setUsernameError(null)
            } else {
                setIsUsernameValid(false)
                setUsernameError("Username is already taken")
            }
        } catch (error) {
            console.error("Username check error:", error)
            setIsUsernameValid(false)
            setUsernameError("Error checking username availability")
        } finally {
            setIsUsernameChecking(false)
        }
    }

    const onSubmit = async (data: FormData) => {
        setSubmitError(null)

        startTransition(async () => {
            try {
                const formData = new FormData()
                formData.append("username", data.username)
                formData.append("dob", data.dob.toISOString())

                const result = await createUserAccount(data.username, data.dob)

                if (!result.success) {
                    setSubmitError(result.error || "Failed to create account")
                }
            } catch (error) {
                console.error("Account creation error:", error)
                setSubmitError("An unexpected error occurred")
            }
        })
    }

    const isFormValid = form.formState.isValid && isUsernameValid && !isUsernameChecking

    return (
        <div className="min-h-screen bg-white flex items-center justify-center p-4 animate-in fade-in duration-300">
            <Card className="w-full max-w-md">
                <CardHeader className="text-center">
                    <CardTitle className="text-3xl font-bold text-black">Complete Your Profile</CardTitle>
                    <CardDescription className="text-gray-600">
                        Welcome! Please provide some additional information to complete your account setup.
                    </CardDescription>
                    {userEmail && (
                        <div className="bg-gray-50 border border-gray-200 rounded-lg p-3 mt-4">
                            <p className="text-sm text-gray-600">Signing up as:</p>
                            <p className="text-black font-medium">{userEmail}</p>
                        </div>
                    )}
                </CardHeader>
                <CardContent>
                    <Form {...form}>
                        <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
                            <FormField
                                control={form.control}
                                name="username"
                                render={({field}) => (
                                    <FormItem>
                                        <FormLabel>Username</FormLabel>
                                        <FormControl>
                                            <div className="relative">
                                                <Input
                                                    placeholder="Enter your username"
                                                    {...field}
                                                    onBlur={(e) => {
                                                        field.onBlur()
                                                        checkUsername(e.target.value)
                                                    }}
                                                    onChange={(e) => {
                                                        field.onChange(e)
                                                        setUsernameError(null)
                                                        setIsUsernameValid(false)
                                                    }}
                                                    className={cn(
                                                        isUsernameValid && "border-green-500",
                                                        usernameError && "border-red-500"
                                                    )}
                                                />
                                                {isUsernameChecking && (
                                                    <Loader2
                                                        className="absolute right-3 top-3 h-4 w-4 animate-spin text-gray-400"/>
                                                )}
                                            </div>
                                        </FormControl>
                                        <FormDescription>
                                            Choose a unique username (3-20 characters, letters, numbers, and underscores
                                            only)
                                        </FormDescription>
                                        {usernameError && (
                                            <p className="text-sm text-red-600">{usernameError}</p>
                                        )}
                                        {isUsernameValid && !usernameError && (
                                            <p className="text-sm text-green-600">Username is available!</p>
                                        )}
                                        <FormMessage/>
                                    </FormItem>
                                )}
                            />

                            <FormField
                                control={form.control}
                                name="dob"
                                render={({field}) => (
                                    <FormItem className="flex flex-col">
                                        <FormLabel>Date of Birth</FormLabel>
                                        <Popover>
                                            <PopoverTrigger asChild>
                                                <FormControl>
                                                    <Button
                                                        variant="outline"
                                                        className={cn(
                                                            "w-full pl-3 text-left font-normal",
                                                            !field.value && "text-muted-foreground"
                                                        )}
                                                    >
                                                        {field.value ? (
                                                            format(field.value, "PPP")
                                                        ) : (
                                                            <span>Pick a date</span>
                                                        )}
                                                        <CalendarIcon className="ml-auto h-4 w-4 opacity-50"/>
                                                    </Button>
                                                </FormControl>
                                            </PopoverTrigger>
                                            <PopoverContent className="w-auto p-0" align="start" side="bottom"
                                                            sideOffset={4}>
                                                <div className="h-[320px] flex flex-col">
                                                    <Calendar
                                                        mode="single"
                                                        selected={field.value}
                                                        captionLayout="dropdown"
                                                        onSelect={(date) => {
                                                            field.onChange(date)
                                                            if (date) {
                                                                form.trigger("dob")
                                                            }
                                                        }}
                                                        disabled={(date) =>
                                                            date > new Date() || date < new Date("1900-01-01")
                                                        }
                                                        autoFocus
                                                        fixedWeeks
                                                    />
                                                </div>
                                            </PopoverContent>
                                        </Popover>
                                        <FormDescription>
                                            You must be at least 18 years old to create an account
                                        </FormDescription>
                                        <FormMessage/>
                                    </FormItem>
                                )}
                            />

                            {submitError && (
                                <div className="bg-red-50 border border-red-200 rounded-lg p-3">
                                    <p className="text-sm text-red-600">{submitError}</p>
                                </div>
                            )}

                            <Button
                                type="submit"
                                className="w-full"
                                disabled={!isFormValid || isPending}
                            >
                                {isPending ? (
                                    <>
                                        <Loader2 className="mr-2 h-4 w-4 animate-spin"/>
                                        Creating Account...
                                    </>
                                ) : (
                                    "Create Account"
                                )}
                            </Button>
                        </form>
                    </Form>
                </CardContent>
            </Card>
        </div>
    )
}