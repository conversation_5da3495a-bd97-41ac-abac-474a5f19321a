"use client";

import {trpc} from "@/projects/web/lib/trpc-client";

export default function ClientExample() {
    const {data, error, isLoading} = trpc.greet.useQuery({name: "Client"});

    if (isLoading) {
        return <div>Loading...</div>;
    }
    if (error) {
        return <div>Error: {error.message}</div>;
    }

    return (
        <div>
            <p>Message from tRPC (CSR):</p>
            <p>{data}</p>
        </div>
    );
} 