{"name": "friend-cards", "version": "0.1.0", "private": true, "scripts": {"build": "IS_BUILD=true next build", "dev": "next dev --turbopack", "lint": "next lint", "lint:fix": "next lint --fix", "start": "next start", "db:generate": "drizzle-kit generate", "db:migrate": "drizzle-kit migrate", "db:generate-and-migrate": "drizzle-kit generate && drizzle-kit migrate", "db:studio": "drizzle-kit studio"}, "dependencies": {"@hookform/resolvers": "^3.10.0", "@openauthjs/openauth": "^0.4.3", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-popover": "^1.1.14", "@radix-ui/react-slot": "^1.2.3", "@tanstack/react-query": "^5.81.2", "@tidbcloud/serverless": "^0.2.0", "@trpc/client": "^11.4.1", "@trpc/next": "^11.4.1", "@trpc/react-query": "^11.4.1", "@trpc/server": "^11.4.1", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "date-fns": "^4.1.0", "drizzle-kit": "^0.31.4", "drizzle-orm": "^0.44.2", "lucide-react": "^0.525.0", "mysql2": "^3.14.2", "next": "15.3.1", "react": "^19.0.0", "react-day-picker": "^9.8.0", "react-dom": "^19.0.0", "react-hook-form": "^7.60.0", "server-only": "^0.0.1", "tailwind-merge": "^3.3.1", "zod": "^3.25.76"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-next": "15.3.1", "eslint-plugin-unused-imports": "^4.1.4", "tailwindcss": "^4", "tw-animate-css": "^1.3.5", "typescript": "^5"}}