# Friend Cards

This is a [Next.js](https://nextjs.org) project with tRPC integration, deployed on Vercel with environment variables managed by SST.

## Architecture

- **Frontend**: Next.js app deployed on Vercel
- **API**: tRPC endpoints running as Vercel serverless functions
- **Environment Management**: SST manages environment variables and pushes them to Vercel
- **CI/CD**: GitHub Actions handles environment variable deployment via SST

## Getting Started

### Prerequisites

1. Node.js 18+ installed
2. Vercel account with a project created
3. AWS account (for SST)
4. Vercel API token
5. GitHub repository connected to Vercel

### Local Development

1. Install dependencies:
```bash
npm install
```

2. Run the development server:
```bash
npm run dev
```

3. Open [http://localhost:3000](http://localhost:3000) with your browser to see the result.

### Environment Setup

#### 1. Vercel Configuration

1. Create a Vercel project and connect it to your GitHub repository
2. Get your Vercel Project ID from Project Settings > General
3. Create a Vercel API token at https://vercel.com/account/tokens

#### 2. SST Configuration

1. Update `sst.config.ts` and replace `"your-vercel-project-name"` with your actual Vercel project ID
2. Set up environment variables:
   - `VERCEL_API_TOKEN`: Your Vercel API token
   - `CLOUDFLARE_API_TOKEN`: For DNS management (if using Cloudflare)
   - `CLOUDFLARE_DEFAULT_ACCOUNT_ID`: Your Cloudflare account ID

#### 3. GitHub Secrets

Add these secrets to your GitHub repository:
- `VERCEL_API_TOKEN`
- `CLOUDFLARE_API_TOKEN`
- `CLOUDFLARE_DEFAULT_ACCOUNT_ID`
- `CI_ARN_NAME`: AWS IAM role for GitHub Actions

### Deployment

#### Environment Variables Only (SST)

Deploy environment variables to staging:
```bash
npm run env:staging
```

Deploy environment variables to production:
```bash
npm run env:production
```

#### Next.js App (Vercel)

The Next.js app is automatically deployed by Vercel when you push to your connected GitHub repository. No manual deployment needed!

## Project Structure

```
├── projects/web/          # Next.js application
│   ├── app/              # App router pages and API routes
│   ├── lib/              # tRPC configuration and utilities
│   └── package.json      # Web app dependencies
├── sst.config.ts         # SST configuration for environment variables
├── vercel.json           # Vercel deployment configuration
└── package.json          # Root package.json with SST scripts
```

## Scripts

- `npm run dev` - Start development server
- `npm run build` - Build the Next.js app
- `npm run lint` - Run ESLint
- `npm run env:staging` - Deploy environment variables to staging
- `npm run env:production` - Deploy environment variables to production

## Learn More

- [Next.js Documentation](https://nextjs.org/docs)
- [tRPC Documentation](https://trpc.io/docs)
- [SST Documentation](https://sst.dev/docs)
- [Vercel Documentation](https://vercel.com/docs)
