#!/usr/bin/env bash

set -euo pipefail

STAGE="${1:-staging}"
DB_NAME="${DB_NAME:-friend_cards}"

command -v ticloud >/dev/null || {
  echo "✖ TiDB Cloud CLI (ticloud) not found. Install it first." >&2
  echo "  curl -sSfL https://raw.githubusercontent.com/tidbcloud/tidbcloud-cli/main/install.sh | sh" >&2
  exit 1
}

[[ -z "${TIDB_CLUSTER_ID:-}" ]] && {
  echo "✖ TIDB_CLUSTER_ID must be set in the environment." >&2
  exit 1
}

# Figure out which host / branch we need
if [[ "${STAGE}" == "production" ]]; then
  echo "🔎 Fetching production cluster info …" >&2
  cluster_json=$(ticloud serverless describe -c "${TIDB_CLUSTER_ID}")
  host=$(echo "${cluster_json}" | jq -r '.endpoints.public.host // .connection_strings.standard | capture("^[^@]*@(?<h>[^:/]+)") .h')
  port=$(echo "${cluster_json}"  | jq -r '.endpoints.public.port // 4000')
  branch_id=""             # production uses the default branch
else
  # → CI feature branch (same sanitising rules as TiDB console)
  raw_branch="${CIRCLE_BRANCH:-$(git rev-parse --abbrev-ref HEAD 2>/dev/null || echo staging)}"
  branch="${raw_branch,,}"                              # lower-case
  branch="${branch//[^a-z0-9-]/-}"                     # keep safe chars
  branch="${branch%-}"                                  # trim trailing -
  branch="${branch:0:50}"                               # TiDB limit



  echo "🔎 Looking for TiDB branch “${branch}” …" >&2
  branches_json=$(ticloud serverless branch list -c "${TIDB_CLUSTER_ID}" -o json)
  branch_id=$(echo "${branches_json}" | jq -r --arg b "${branch}" '.branches[] | select(.displayName==$b) | .branchId')

  [[ -z "${branch_id}" ]] && {
    echo "✖ Branch “${branch}” not found in cluster; aborting (no auto-create here)." >&2
    exit 1
  }

  branch_json=$(ticloud serverless branch describe -c "${TIDB_CLUSTER_ID}" -b "${branch_id}")
  host=$(echo "${branch_json}" | jq -r '.endpoints.public.host')
  port=$(echo "${branch_json}"  | jq -r '.endpoints.public.port // 4000')
fi

[[ -z "${host}" ]] && {
  echo "✖ Could not determine public endpoint hostname from TiDB Cloud API." >&2
  exit 1
}


# Ensure we have an SQL user + password
want_user="${SQL_USER:-app_user}"
echo "🔎 Checking SQL users (want “${want_user}”) …" >&2
users_json=$(ticloud serverless sql-user list -c "${TIDB_CLUSTER_ID}" -o json)
have_user=$(echo "${users_json}" | jq -r --arg u "${want_user}" '.sqlUsers[] | select((.userName | split(".")[1]) == $u) | .userName')

if [[ -z "${have_user}" ]]; then
  echo "🆕 Creating SQL user “${want_user}” …" >&2
  password="$(openssl rand -base64 16 | tr -d '/+=')"
  ticloud serverless sql-user create \
      -c "${TIDB_CLUSTER_ID}" \
      --user "${want_user}" \
      --password "${password}" \
      --role "role_readwrite"
else
  echo "✅  User exists – generating fresh password …" >&2
  password="$(openssl rand -base64 16 | tr -d '/+=')"
  ticloud serverless sql-user update \
      -c "${TIDB_CLUSTER_ID}" \
      --user "${want_user}" \
      --password "${password}" >&2
fi

echo "🔎 Fetching user userPrefix" >&2
cluster_json=$(ticloud serverless describe -c "${TIDB_CLUSTER_ID}")
userPrefix=$(echo "${cluster_json}" | jq -r '.userPrefix')

echo "mysql://${userPrefix}.${want_user}:${password}@${host}:${port}/${DB_NAME}?sslaccept=strict"
