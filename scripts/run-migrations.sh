#!/usr/bin/env bash

# Script to run Drizzle migrations for the specified stage
set -e

STAGE=${1:-staging}

echo "🚀 Running migrations for stage: $STAGE"

if [ -z "$DATABASE_URL" ]; then
    echo "❌ Error: Could not determine database URL for stage $STAGE"
    exit 1
fi

echo "🔗 Using database URL: ${DATABASE_URL:0:30}..." # Show first 30 chars for context

# Export DATABASE_URL for drizzle-kit
export DATABASE_URL

cd projects/web

# Check if migrations directory exists
if [ ! -d "drizzle" ]; then
    echo "❌ Error: Drizzle migrations directory not found"
    exit 1
fi

# Count migration files
MIGRATION_COUNT=$(find drizzle -name "*.sql" | wc -l)
echo "📁 Found $MIGRATION_COUNT migration files"

# Run migrations with error handling
echo "⚡ Running Drizzle migrations..."
if npm run db:migrate; then
    echo "✅ Migrations completed successfully for stage: $STAGE on branch $CIRCLE_BRANCH"
else
    echo "❌ Migration failed for stage: $STAGE"
    exit 1
fi