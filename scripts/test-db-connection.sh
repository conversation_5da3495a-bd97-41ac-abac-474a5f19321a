#!/usr/bin/env bash

# Script to test database connection before running migrations
set -e

STAGE=${1:-staging}

echo "🔍 Testing database connection for stage: $STAGE"

# Get the database URL
DATABASE_URL=$(./scripts/get-tidb-url.sh "$STAGE")

if [ -z "$DATABASE_URL" ]; then
    echo "❌ Error: Could not get database URL"
    exit 1
fi

# Export for drizzle-kit
export DATABASE_URL

# Navigate to web project directory
cd projects/web

# Test connection by trying to run a simple drizzle-kit command
echo "🔗 Testing connection to: ${DATABASE_URL:0:30}..."

# Use drizzle-kit to test the connection
if timeout 30 npx drizzle-kit introspect --config=./drizzle.config.ts > /dev/null 2>&1; then
    echo "✅ Database connection successful"
else
    echo "❌ Database connection failed"
    echo "Please check:"
    echo "  - Database URL format"
    echo "  - Network connectivity"
    echo "  - Database credentials"
    echo "  - Database server status"
    exit 1
fi