#!/usr/bin/env bash

# locate the .env relative to this script
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_SLUG="gh/my-org/my-repo"
ENV_FILE="$SCRIPT_DIR/../.env"

while IFS='=' read -r KEY VALUE; do
  # Skip if KEY is empty, or starts with #, \, or ;
  case "$KEY" in
    ""|\#*|\\*|\;*) continue ;;
  esac

  echo "Setting $KEY..."
  circleci env var set "$KEY" "$VALUE" --project-slug "$PROJECT_SLUG"
done < "$ENV_FILE"
