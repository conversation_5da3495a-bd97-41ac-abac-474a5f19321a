// eslint-disable-next-line @typescript-eslint/triple-slash-reference
/// <reference path="./.sst/platform/config.d.ts" />

export default $config({
    app(input) {
        return {
            name: "friend-cards",
            removal: input?.stage === "production" ? "retain" : "remove",
            protect: ["production"].includes(input?.stage),
            home: "aws",
            providers: {vercel: "1.15.0"},
        };
    },
    async run() {
        const baseUrl =
            $app.stage === "production"
                ? "friend-cards.com"
                : `${$app.stage}.friend-cards.com`;
        const env = $app.stage === "production" ? "production" : "staging";
        const vercelProjectId = process.env.VERCEL_PROJECT_ID!;

        // Create OpenAuth issuer
        const auth = new sst.aws.Auth("Auth", {
            issuer: {
                handler: "./projects/web/authServer/auth.handler",
                environment: {
                    GOOGLE_CLIENT_ID: process.env.GOOGLE_CLIENT_ID!,
                    GOOGLE_CLIENT_SECRET: process.env.GOOGLE_CLIENT_SECRET!,
                    DISCORD_CLIENT_ID: process.env.DISCORD_CLIENT_ID!,
                    DISCORD_CLIENT_SECRET: process.env.DISCORD_CLIENT_SECRET!,
                    DATABASE_URL: process.env.DATABASE_URL!,
                },
            },
            domain: {
                name: `auth.${baseUrl}`,
                dns: sst.cloudflare.dns()
            }
        });


        if ($dev) {
            const site = new sst.aws.Nextjs("Web", {
                path: "projects/web",
                environment: {
                    NEXT_PUBLIC_APP_URL: `https://${$app.stage}.friend-cards.com`,
                    SST_STAGE: $app.stage,
                    OPENAUTH_ISSUER: auth.url,
                    DATABASE_URL: process.env.DATABASE_URL!,
                },
            });
            return {
                auth: auth.url,
            };
        }

        const envVars = [
            ["NEXT_PUBLIC_APP_URL", `https://${baseUrl}`],
            ["SST_STAGE", env],
        ];

        envVars.forEach(([key, value]) => {
            new vercel.ProjectEnvironmentVariable(key, {
                    projectId: vercelProjectId,
                    key: key,
                    value: value,
                    targets: env === "production" ? ["production"] : ["preview"],
                },
                {
                    deleteBeforeReplace: true,
                }
            );
        });

        new vercel.ProjectEnvironmentVariable("OPENAUTH_ISSUER", {
                projectId: vercelProjectId,
                key: "OPENAUTH_ISSUER",
                value: auth.url,
                targets: env === "production" ? ["production"] : ["preview"],
            },
            {
                deleteBeforeReplace: true,
            }
        );


        return {
            auth: auth.url,
        };
    },
});
